import Database from 'better-sqlite3';
import path from 'path';
import fs from 'fs';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

// Get __dirname equivalent for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// We'll import logger dynamically when needed

// Function to get the current date in YYYY-MM-DD format
const getCurrentDate = () => {
  const now = new Date();
  return `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')}`;
};

// Function to get the database file path
const getDBFilePath = () => {
  const dbDir = path.join(__dirname, 'data');
  // Ensure the data directory exists
  if (!fs.existsSync(dbDir)) {
    fs.mkdirSync(dbDir, { recursive: true });
  }
  return path.join(dbDir, `ticks-${getCurrentDate()}.sqlite`);
};

/**
 * Fetches trading pair data from the SQLite database.
 * @param {string} symbol - The trading pair symbol (e.g., 'btcusdt').
 * @returns {Promise<{lastRecordTime: number | null, totalRecords: number, closedKlineCount: number}>}
 */
const getTradingPairData = async (symbol) => {
  // Dynamic import for logger
  const logger = (await import('./logger.js')).default;

  const dbFile = getDBFilePath();
  let db;

  try {
    db = new Database(dbFile);
    const tableName = symbol.toLowerCase();

    // Check if table exists
    const tableExists = db.prepare(`SELECT name FROM sqlite_master WHERE type='table' AND name=?`).get(tableName);
    if (!tableExists) {
      logger.warn(`Table for symbol "${symbol}" does not exist in ${dbFile}.`);
      return { lastRecordTime: null, totalRecords: 0, closedKlineCount: 0 };
    }

    // Query for last record time (kline_end_time)
    const lastRecordQuery = db.prepare(`SELECT kline_end_time FROM "${tableName}" ORDER BY kline_end_time DESC LIMIT 1`);
    const lastRecordResult = lastRecordQuery.get();
    const lastRecordTime = lastRecordResult ? lastRecordResult.kline_end_time : null;

    // Query for total number of records
    const totalRecordsQuery = db.prepare(`SELECT COUNT(*) as count FROM "${tableName}"`);
    const totalRecordsResult = totalRecordsQuery.get();
    const totalRecords = totalRecordsResult ? totalRecordsResult.count : 0;

    // Query for number of closed klines
    const closedKlineQuery = db.prepare(`SELECT COUNT(*) as count FROM "${tableName}" WHERE kline_is_closed = 1`);
    const closedKlineResult = closedKlineQuery.get();
    const closedKlineCount = closedKlineResult ? closedKlineResult.count : 0;

    return { lastRecordTime, totalRecords, closedKlineCount };

  } catch (err) {
    logger.error(`Error fetching data for symbol ${symbol} from ${dbFile}: ${err.message}`);
    return { lastRecordTime: null, totalRecords: 0, closedKlineCount: 0 };
  } finally {
    if (db) {
      db.close();
    }
  }
};

export { getTradingPairData };
