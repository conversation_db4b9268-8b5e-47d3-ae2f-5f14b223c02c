// Trading pairs component

import { useState, useMemo, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  Play, 
  Pause, 
  Search, 
  Filter,
  AlertCircle,
  CheckCircle,
  Clock,
  MinusCircle,
  ArrowUpDown // For sorting icon
} from 'lucide-react';
import { SystemStatus, TradingPair as TradingPairType } from '../../lib/types';
import { getTradingPairData } from '@root/fetchers/db-query-utils'; // Import the DB utility

// Define the status type for pairs
type PairStatus = 'active' | 'warning' | 'error' | 'stopped';

// Define sorting configuration
type SortConfig = {
  key: keyof TradingPairType | null;
  direction: 'ascending' | 'descending';
};

interface TradingPairsProps {
  status: SystemStatus | null;
  loading: boolean;
  onStartPair: (pair: string) => void;
  onStopPair: (pair: string) => void;
}

export function TradingPairs({ status, loading, onStartPair, onStopPair }: TradingPairsProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<PairStatus | 'all'>('all');
  const [dbDataLoading, setDbDataLoading] = useState(false);
  const [tradingPairDbStats, setTradingPairDbStats] = useState<{ [symbol: string]: { lastRecordTime: number | null; totalRecords: number; closedKlineCount: number } }>({});
  const [sortConfig, setSortConfig] = useState<SortConfig>({ key: null, direction: 'ascending' });

  // Fetch database stats for all trading pairs
  useEffect(() => {
    const fetchDbStats = async () => {
      if (!status?.system?.tickCollector) {
        setTradingPairDbStats({});
        return;
      }

      setDbDataLoading(true);
      const symbols = Object.keys(status.system.tickCollector);
      const statsPromises = symbols.map(async (symbol) => {
        try {
          const data = await getTradingPairData(symbol);
          return { [symbol]: data };
        } catch (error) {
          console.error(`Error fetching DB stats for ${symbol}:`, error);
          return { [symbol]: { lastRecordTime: null, totalRecords: 0, closedKlineCount: 0 } };
        }
      });

      try {
        const results = await Promise.all(statsPromises);
        const combinedStats = Object.assign({}, ...results);
        setTradingPairDbStats(combinedStats);
      } catch (error) {
        console.error("Error fetching all DB stats:", error);
      } finally {
        setDbDataLoading(false);
      }
    };

    fetchDbStats();
  }, [status?.system?.tickCollector]); // Re-fetch if tickCollector changes

  // Process and combine data for display
  const processedTradingPairs = useMemo(() => {
    if (!status?.system?.tickCollector) return [];

    const pairsData: TradingPairType[] = Object.keys(status.system.tickCollector).map((connectionIdStr: string) => {
      const connectionId = parseInt(connectionIdStr, 10); // Parse the string key to a number
      const conn = status.system.tickCollector[connectionId]; // Access using the numeric key

      const symbol = conn.url.split('/').pop()?.toLowerCase() || ''; // Assuming symbol is in URL
      const dbStats = tradingPairDbStats[symbol] || { lastRecordTime: null, totalRecords: 0, closedKlineCount: 0 };
      
      // Determine status based on connection and potentially DB data (for now, using connection status)
      let pairStatus: PairStatus = 'stopped';
      if (conn.isConnected) {
        pairStatus = 'active'; // Default to active if connected
        // Add logic here to determine 'warning' or 'error' based on dbStats if needed
      } else if (conn.reconnectAttempts > 0) {
        pairStatus = 'warning'; // Example: show warning if reconnecting
      }

      return {
        symbol: symbol,
        status: pairStatus,
        lastUpdate: Date.now() - conn.timeSinceLastData, // Use timeSinceLastData for lastUpdate
        errorCount: conn.reconnectAttempts, // Use reconnectAttempts as errorCount for now
        recordCount: dbStats.totalRecords, // Use totalRecords from DB
        lastRecordTime: dbStats.lastRecordTime,
        totalRecords: dbStats.totalRecords,
        closedKlineCount: dbStats.closedKlineCount,
      };
    });
    
    return pairsData;
  }, [status?.system?.tickCollector, tradingPairDbStats]);

  // Filter pairs based on search term and status filter
  const filteredPairs = useMemo(() => {
    const sortedPairs = [...processedTradingPairs]; // Changed 'let' to 'const'

    // Apply sorting
    if (sortConfig.key !== null) {
      sortedPairs.sort((a, b) => {
        const aValue = a[sortConfig.key as keyof TradingPairType];
        const bValue = b[sortConfig.key as keyof TradingPairType];

        if (aValue === null || bValue === null) {
          if (aValue === null && bValue === null) return 0;
          if (aValue === null) return sortConfig.direction === 'ascending' ? 1 : -1;
          return sortConfig.direction === 'ascending' ? -1 : 1;
        }

        if (aValue < bValue) {
          return sortConfig.direction === 'ascending' ? -1 : 1;
        }
        if (aValue > bValue) {
          return sortConfig.direction === 'ascending' ? 1 : -1;
        }
        return 0;
      });
    }

    // Apply search and status filter
    return sortedPairs.filter(pair => {
      const matchesSearch = pair.symbol.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesStatus = statusFilter === 'all' || pair.status === statusFilter;
      return matchesSearch && matchesStatus;
    });
  }, [processedTradingPairs, searchTerm, statusFilter, sortConfig]);
  
  // Get status icon and color
  const getStatusIcon = (status: PairStatus) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'warning':
        return <AlertCircle className="h-4 w-4 text-yellow-500" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      case 'stopped':
        return <MinusCircle className="h-4 w-4 text-gray-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };
  
  const getStatusColor = (status: PairStatus) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'warning': return 'bg-yellow-100 text-yellow-800';
      case 'error': return 'bg-red-100 text-red-800';
      case 'stopped': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };
  
  // Format time since last update (from connection)
  const formatTimeSince = (timestamp: number) => {
    const seconds = Math.floor((Date.now() - timestamp) / 1000);
    if (seconds < 60) return `${seconds}s ago`;
    const minutes = Math.floor(seconds / 60);
    if (minutes < 60) return `${minutes}m ago`;
    const hours = Math.floor(minutes / 60);
    return `${hours}h ago`;
  };

  // Format timestamp from DB
  const formatDbTimestamp = (timestamp: number | null) => {
    if (timestamp === null) return 'N/A';
    const date = new Date(timestamp);
    return date.toLocaleString(); // Or a more specific date formatting
  };
  
  // Loading skeleton
  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Trading Pairs</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex gap-2">
              <div className="relative flex-1">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <div className="h-9 w-full bg-gray-200 rounded-md animate-pulse pl-8" />
              </div>
              <div className="h-9 w-32 bg-gray-200 rounded-md animate-pulse" />
            </div>
            
            <div className="rounded-md border">
              <div className="h-10 bg-gray-100 border-b" />
              {[...Array(5)].map((_, i) => (
                <div key={i} className="h-16 border-b last:border-b-0 flex items-center px-4">
                  <div className="h-4 w-full bg-gray-200 rounded animate-pulse" />
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>Trading Pairs</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Search and Filter */}
          <div className="flex gap-2">
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search pairs..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>
            
            <div className="flex gap-2">
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value as PairStatus | 'all')}
                className="flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50"
              >
                <option value="all">All Status</option>
                <option value="active">Active</option>
                <option value="warning">Warning</option>
                <option value="error">Error</option>
                <option value="stopped">Stopped</option>
              </select>
            </div>
          </div>
          
          {/* Trading Pairs Table */}
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead onClick={() => setSortConfig({ key: 'symbol', direction: sortConfig.key === 'symbol' ? (sortConfig.direction === 'ascending' ? 'descending' : 'ascending') : 'ascending' })} className="cursor-pointer">
                    Symbol {sortConfig.key === 'symbol' && <ArrowUpDown className="h-4 w-4 inline" />}
                  </TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead onClick={() => setSortConfig({ key: 'lastUpdate', direction: sortConfig.key === 'lastUpdate' ? (sortConfig.direction === 'ascending' ? 'descending' : 'ascending') : 'ascending' })} className="cursor-pointer">
                    Last Update {sortConfig.key === 'lastUpdate' && <ArrowUpDown className="h-4 w-4 inline" />}
                  </TableHead>
                  <TableHead onClick={() => setSortConfig({ key: 'errorCount', direction: sortConfig.key === 'errorCount' ? (sortConfig.direction === 'ascending' ? 'descending' : 'ascending') : 'ascending' })} className="cursor-pointer">
                    Error Count {sortConfig.key === 'errorCount' && <ArrowUpDown className="h-4 w-4 inline" />}
                  </TableHead>
                  <TableHead onClick={() => setSortConfig({ key: 'recordCount', direction: sortConfig.key === 'recordCount' ? (sortConfig.direction === 'ascending' ? 'descending' : 'ascending') : 'ascending' })} className="cursor-pointer">
                    Records {sortConfig.key === 'recordCount' && <ArrowUpDown className="h-4 w-4 inline" />}
                  </TableHead>
                  {/* New Columns */}
                  <TableHead onClick={() => setSortConfig({ key: 'lastRecordTime', direction: sortConfig.key === 'lastRecordTime' ? (sortConfig.direction === 'ascending' ? 'descending' : 'ascending') : 'ascending' })} className="cursor-pointer">
                    Last Record Time {sortConfig.key === 'lastRecordTime' && <ArrowUpDown className="h-4 w-4 inline" />}
                  </TableHead>
                  <TableHead onClick={() => setSortConfig({ key: 'totalRecords', direction: sortConfig.key === 'totalRecords' ? (sortConfig.direction === 'ascending' ? 'descending' : 'ascending') : 'ascending' })} className="cursor-pointer">
                    Total Records {sortConfig.key === 'totalRecords' && <ArrowUpDown className="h-4 w-4 inline" />}
                  </TableHead>
                  <TableHead onClick={() => setSortConfig({ key: 'closedKlineCount', direction: sortConfig.key === 'closedKlineCount' ? (sortConfig.direction === 'ascending' ? 'descending' : 'ascending') : 'ascending' })} className="cursor-pointer">
                    Closed Kline Count {sortConfig.key === 'closedKlineCount' && <ArrowUpDown className="h-4 w-4 inline" />}
                  </TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredPairs.length > 0 ? (
                  filteredPairs.map((pair) => (
                    <TableRow key={pair.symbol}>
                      <TableCell className="font-medium uppercase">
                        {pair.symbol}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {getStatusIcon(pair.status)}
                          <Badge className={getStatusColor(pair.status)}>
                            {pair.status.charAt(0).toUpperCase() + pair.status.slice(1)}
                          </Badge>
                        </div>
                      </TableCell>
                      <TableCell>
                        {formatTimeSince(pair.lastUpdate)}
                      </TableCell>
                      <TableCell>
                        {pair.errorCount}
                      </TableCell>
                      <TableCell>
                        {pair.recordCount.toLocaleString()}
                      </TableCell>
                      {/* New Data Cells */}
                      <TableCell>
                        {dbDataLoading ? (
                          <div className="h-4 w-20 bg-gray-200 rounded animate-pulse" />
                        ) : (
                          formatDbTimestamp(pair.lastRecordTime)
                        )}
                      </TableCell>
                      <TableCell>
                        {dbDataLoading ? (
                          <div className="h-4 w-16 bg-gray-200 rounded animate-pulse" />
                        ) : (
                          pair.totalRecords.toLocaleString()
                        )}
                      </TableCell>
                      <TableCell>
                        {dbDataLoading ? (
                          <div className="h-4 w-16 bg-gray-200 rounded animate-pulse" />
                        ) : (
                          pair.closedKlineCount.toLocaleString()
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-2">
                          {pair.status === 'stopped' ? (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => onStartPair(pair.symbol)}
                            >
                              <Play className="h-4 w-4 mr-1" />
                              Start
                            </Button>
                          ) : (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => onStopPair(pair.symbol)}
                            >
                              <Pause className="h-4 w-4 mr-1" />
                              Stop
                            </Button>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={9} className="h-24 text-center"> {/* Adjusted colSpan */}
                      {dbDataLoading ? 'Loading trading pair data...' : 'No pairs found'}
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
          
          {/* Bulk Actions */}
          <div className="flex justify-end gap-2">
            <Button variant="outline">
              Start All
            </Button>
            <Button variant="outline">
              Stop All
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
