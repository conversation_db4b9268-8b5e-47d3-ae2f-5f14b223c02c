// Main dashboard page
'use client';

import { useSystemStatus } from '@/hooks/useSystemStatus';
import { SystemOverview } from '@/components/dashboard/system-overview';
import { PerformanceCharts } from '@/components/dashboard/performance-charts';
import { TradingPairs } from '@/components/dashboard/trading-pairs';
import { ProblemPairs } from '@/components/dashboard/problem-pairs';
import { SystemDetails } from '@/components/dashboard/system-details';
import { ActivityLog } from '@/components/dashboard/activity-log';
import { Button } from '@/components/ui/button';
import { RefreshCw } from 'lucide-react';
import { useState } from 'react';

export default function Dashboard() {
  const { health, status, metrics, loading, error, refresh } = useSystemStatus();
  const [actionLoading, setActionLoading] = useState(false);
  
  const handleStartPair = async (pair: string) => {
    setActionLoading(true);
    try {
      // In a real implementation, this would call the API
      console.log(`Starting pair: ${pair}`);
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      // Refresh data after action
      refresh();
    } catch (err) {
      console.error(`Error starting pair ${pair}:`, err);
    } finally {
      setActionLoading(false);
    }
  };
  
  const handleStopPair = async (pair: string) => {
    setActionLoading(true);
    try {
      // In a real implementation, this would call the API
      console.log(`Stopping pair: ${pair}`);
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      // Refresh data after action
      refresh();
    } catch (err) {
      console.error(`Error stopping pair ${pair}:`, err);
    } finally {
      setActionLoading(false);
    }
  };
  
  const handleRestartPair = async (pair: string) => {
    setActionLoading(true);
    try {
      // In a real implementation, this would call the API
      console.log(`Restarting pair: ${pair}`);
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      // Refresh data after action
      refresh();
    } catch (err) {
      console.error(`Error restarting pair ${pair}:`, err);
    } finally {
      setActionLoading(false);
    }
  };
  
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <div className="container max-w-7xl py-8">
        {/* Header */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-8">
          <div>
            <h1 className="text-3xl font-bold">Binance Tick Collector</h1>
            <p className="text-muted-foreground">
              Real-time monitoring dashboard
            </p>
          </div>
          
          <div className="flex items-center gap-2">
            <Button 
              onClick={refresh} 
              disabled={loading || actionLoading}
              variant="outline"
            >
              <RefreshCw 
                className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} 
              />
              Refresh
            </Button>
          </div>
        </div>
        
        {/* Error Banner */}
        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center text-red-800">
              <span className="font-medium">Error:</span>
              <span className="ml-2">{error}</span>
            </div>
          </div>
        )}
        
        {/* System Overview Cards */}
        <div className="mb-8">
          <SystemOverview 
            status={status} 
            metrics={metrics} 
            loading={loading} 
          />
        </div>
        
        {/* Performance Charts */}
        <div className="mb-8">
          <PerformanceCharts 
            metrics={metrics} 
            loading={loading} 
          />
        </div>
        
        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
          {/* Left Column - Trading Pairs */}
          <div className="lg:col-span-2">
            <TradingPairs 
              status={status} 
              loading={loading} 
              onStartPair={handleStartPair}
              onStopPair={handleStopPair}
            />
          </div>
          
          {/* Right Column - Problem Pairs and System Details */}
          <div className="space-y-8">
            <ProblemPairs 
              status={status} 
              loading={loading} 
              onStartPair={handleStartPair}
              onRestartPair={handleRestartPair}
            />
            
            <SystemDetails 
              status={status} 
              metrics={metrics} 
              loading={loading} 
            />
          </div>
        </div>
        
        {/* Activity Log */}
        <div className="mb-8">
          <ActivityLog loading={loading} />
        </div>
      </div>
    </div>
  );
}
