{"dependencies": {"@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@types/node": "^24.1.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "better-sqlite3": "^9.4.3", "bullmq": "^5.1.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^17.2.1", "express": "^4.19.2", "ioredis": "^5.3.2", "lucide-react": "^0.525.0", "next": "^15.4.4", "react": "^19.1.0", "react-dom": "^19.1.0", "recharts": "^3.1.0", "tailwind-merge": "^3.3.1", "typescript": "^5.8.3", "ws": "^8.17.1"}, "devDependencies": {"typescript": "^5", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@tailwindcss/postcss": "^4", "tailwindcss": "^4", "tw-animate-css": "^1.3.6", "eslint": "^9", "eslint-config-next": "15.4.4", "@eslint/eslintrc": "^3"}}